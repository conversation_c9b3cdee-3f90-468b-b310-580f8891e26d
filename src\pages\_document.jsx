import Document, { Html, <PERSON>, Main, NextScript } from "next/document";

class MyDocument extends Document {
	static async getInitialProps(ctx) {
		const initialProps = await Document.getInitialProps(ctx);

		// Check for AMP pages - includes both /amp routes and /web-stories routes
		const isAmp = ctx.req.url?.includes("/amp") || ctx.req.url?.includes("/web-stories/");

		return {
			...initialProps,
			isAmp,
		};
	}

	render() {
		const { isAmp } = this.props;

		return (
			<Html lang="en" {...(isAmp ? { amp: "" } : {})}>
				<Head>
					{isAmp ? (
						<>
							<link
								href="https://fonts.googleapis.com/css2?family=Karla:ital,wght@0,200..800;1,200..800&display=swap"
								rel="stylesheet"
							/>
							<link rel="stylesheet" href="https://use.typekit.net/mgb5ovp.css" />

							<script async src="https://cdn.ampproject.org/v0.js"></script>
							<script
								async
								custom-element="amp-carousel"
								src="https://cdn.ampproject.org/v0/amp-carousel-0.2.js"
							></script>
							{/* <script
								async
								custom-element="amp-next-page"
								src="https://cdn.ampproject.org/v0/amp-next-page-1.0.js"
							></script> */}
							<style amp-boilerplate="">
								{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}
							</style>
							<noscript>
								<style amp-boilerplate="">
									{`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}
								</style>
							</noscript>
						</>
					) : (
						<>
							{/* Your normal page scripts */}
							<link rel="stylesheet" href="https://use.typekit.net/mgb5ovp.css" />
							<link
								rel="stylesheet"
								href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
								integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
								crossOrigin="anonymous"
							/>
							<script async src="https://www.googletagmanager.com/gtag/js?id=G-5TH270W358"></script>
							<script
								dangerouslySetInnerHTML={{
									__html: `
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('js', new Date());
                    gtag('config', 'G-5TH270W358');
                  `,
								}}
							/>
							<script
								dangerouslySetInnerHTML={{
									__html: `
                    !function(f,b,e,v,n,t,s) {
                      if(f.fbq)return;
                      n=f.fbq=function(){n.callMethod?
                      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                      if(!f._fbq)f._fbq=n;
                      n.push=n;
                      n.loaded=!0;
                      n.version='2.0';
                      n.queue=[];
                      t=b.createElement(e);
                      t.async=!0;
                      t.src=v;
                      s=b.getElementsByTagName(e)[0];
                      s.parentNode.insertBefore(t,s)
                    }(window, document,'script',
                    'https://connect.facebook.net/en_US/fbevents.js');
                    
                    fbq('init', '1688101695112078');
                    fbq('track', 'PageView');
                  `,
								}}
							/>
							<noscript>
								<img
									height="1"
									width="1"
									style={{ display: "none" }}
									src="https://www.facebook.com/tr?id=1688101695112078&ev=PageView&noscript=1"
								/>
							</noscript>
						</>
					)}
				</Head>
				<body>
					<Main />
					<NextScript />
					{!isAmp && (
						<>
							<script async src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
							<script
								src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
								integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
								crossOrigin="anonymous"
								async
							></script>
							<script
								src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js"
								integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy"
								crossOrigin="anonymous"
								async
							></script>
						</>
					)}
				</body>
			</Html>
		);
	}
}

export default MyDocument;
