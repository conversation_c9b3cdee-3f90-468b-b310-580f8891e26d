import React from "react";
import { getWebStories } from "@/pages/api/WebstoriesApi";
import Head from "next/head";

import { webStoryDetailCSS } from "@/components/amp/ampCSS";
import { Const } from "@/utils/Constants";
import { dateFormateWithTimeShort } from "@/utils/Util";

export const config = { amp: true };

const WebStoryDetail = ({ data, nextData, breadcrumbs, meta, pathname }) => {
	// AMP-compliant CSS without i-amphtml- prefixes

	return (
		<>
			<style jsx amp-custom>
				{webStoryDetailCSS}
			</style>
			<Head>
				<meta name="amp-to-amp-navigation" content="AMP-Redirect" />
				<script
					async
					src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
					custom-element="amp-story"
				></script>
				<script
					async
					custom-element="amp-story-auto-ads"
					src="https://cdn.ampproject.org/v0/amp-story-auto-ads-0.1.js"
				></script>

				{/* Required canonical link with absolute URL */}
				<link rel="canonical" href={`${Const.ClientLink}${pathname}`} />

				<script
					async
					custom-element="amp-analytics"
					src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"
				></script>
				{/* Add essential meta tags directly */}
				<title>{meta?.title || data?.title || ""}</title>
				<meta name="description" content={meta?.description || meta?.title || ""} />
				<meta property="og:title" content={meta?.og?.title || meta?.title || ""} />
				<meta
					property="og:description"
					content={meta?.og?.description || meta?.description || ""}
				/>
				<meta property="og:url" content={`${Const.ClientLink}${pathname}`} />
				<meta property="og:image" content={meta?.og?.image || `${Const.ClientLink}/logo svg.svg`} />
				<meta property="og:type" content="article" />
				<meta name="twitter:card" content="summary_large_image" />
				<meta
					name="robots"
					content={
						`${meta?.robots}, max-image-preview:large` ||
						"noindex,nofollow, max-image-preview:large"
					}
				/>

				{/* WebPage Schema */}
				<script
					type="application/ld+json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							"@context": "https://schema.org",
							"@type": "WebPage",
							name: meta?.title || data?.title || "",
							description: meta?.description || "",
							speakable: {
								"@type": "SpeakableSpecification",
								xpath: ["//amp-story/@title", "//h1"],
							},
							url: `${Const.ClientLink}${pathname}`,
						}),
					}}
				/>

				{/* NewsArticle Schema */}
				<script
					type="application/ld+json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							"@context": "http://schema.org",
							"@type": "NewsArticle",
							headline: data?.title || "",
							datePublished: data?.timestamp || "",
							dateModified: data?.timestamp || "",
							articleSection: breadcrumbs?.[breadcrumbs.length - 1]?.name || "",
							inLanguage: "en",
							keywords: (data?.tag || []).join(", "),
							description: meta?.description || "",
							url: `${Const.ClientLink}${pathname}`,
							articleBody: data?.slides
								?.map((slide) => slide?.description?.replace(/<[^>]*>/g, "") || "")
								.join(" "),
							mainEntityOfPage: {
								"@type": "WebPage",
								"@id": `${Const.ClientLink}${pathname}`,
							},
							publisher: {
								"@type": "Organization",
								name: Const.Brand,
								url: Const.ClientLink,
								logo: {
									"@type": "ImageObject",
									url: `${Const.ClientLink}/logo svg.svg`,
									width: 200,
									height: 155,
								},
							},
							author: {
								"@type": "Person",
								name: data?.slides?.[0]?.contributor?.[0] || "",
								url: "",
							},
							image: {
								"@type": "ImageObject",
								url: data?.coverImg || "",
								width: 1920,
								height: 1080,
							},
						}),
					}}
				/>

				{/* ImageGallery Schema */}
				<script
					type="application/ld+json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							"@context": "https://schema.org",
							"@type": "ImageGallery",
							url: `${Const.ClientLink}${pathname}`,
							datePublished: data?.timestamp || "",
							mainEntityOfPage: {
								"@type": "WebPage",
								"@id": `${Const.ClientLink}${pathname}`,
								headline: data?.slides?.[0]?.title || "",
								description: data?.slides?.[0]?.description || "",
							},
							image:
								data?.slides?.map((item) => ({
									"@type": "ImageObject",
									url: item?.image || "",
								})) || [],
						}),
					}}
				/>

				{/* MediaGallery Schema */}
				<script
					type="application/ld+json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							"@context": "https://schema.org",
							"@type": "MediaGallery",
							headline: data?.title || "Post Title",
							description: data?.description?.replace(/<[^>]+>/g, "") || "Post Description",
							mainEntityOfPage: {
								"@type": "ImageGallery",
								associatedMedia:
									data?.slides?.map((item) => {
										console.log(item, " ==> items");
										const cleanDescription = item?.description?.replace(/<[^>]+>/g, "") || "";
										return {
											"@type": "ImageObject",
											name: item?.title || "",
											thumbnailUrl: item?.image || "",
											description: cleanDescription,
											contentUrl: item?.image || "",
											caption: item?.title || "",
										};
									}) || [],
							},
						}),
					}}
				/>
			</Head>

			<amp-story
				standalone=""
				title={data?.title || ""}
				publisher="THR"
				publisher-logo-src="/thr-logo.png"
				poster-portrait-src={data?.coverImg || ""}
			>
				{data?.slides?.map((slide, index) => {
					return (
						<React.Fragment key={index}>
							<amp-story-page id={`page-${index}`}>
								<amp-story-grid-layer template="fill">
									<amp-img
										src={slide?.image || ""}
										width="720"
										height="1280"
										layout="fill"
										alt={slide?.altName || ""}
									></amp-img>
								</amp-story-grid-layer>

								{index === 0 && (
									<>
										<amp-story-grid-layer template="fill">
											<div className="logo-gradient-overlay"></div>
										</amp-story-grid-layer>
										<amp-story-grid-layer template="vertical">
											<div className="brand-logo">
												<amp-img
													src="/thr-logo.png"
													width="200"
													height="100"
													layout="fixed"
													alt="Esquire Logo"
												></amp-img>
											</div>
										</amp-story-grid-layer>
									</>
								)}

								<amp-story-grid-layer template="vertical" className="story-content">
									<div className="story-text">
										<h1>{slide?.title || ""}</h1>
										{slide?.description && (
											<div dangerouslySetInnerHTML={{ __html: slide.description }} />
										)}
										{slide?.contributor?.length > 0 && (
											<small>Photo Credit: {slide.contributor.join(", ")}</small>
										)}
										{index === 0 && slide?.timestamp && (
											<small>Published: {dateFormateWithTimeShort(slide.timestamp)}</small>
										)}
									</div>
								</amp-story-grid-layer>
							</amp-story-page>
						</React.Fragment>
					);
				})}

				{/* Next Story Preview Page */}
				{nextData?.slug && nextData?.coverImg && (
					<amp-story-page id="next-story-preview">
						<amp-story-grid-layer template="fill">
							<amp-img
								src={nextData.coverImg}
								width="720"
								height="1280"
								layout="fill"
								alt={nextData.altName || "Next Story"}
							></amp-img>
						</amp-story-grid-layer>

						<amp-story-grid-layer template="vertical" className="next-story-overlay story-content">
							<div className="next-story-preview">
								{/* <div className="preview-label">Next Story</div> */}
								<h2>{nextData.title}</h2>
							</div>
						</amp-story-grid-layer>

						<amp-story-cta-layer>
							<a href={nextData.slug} className="next-story-btn">
								Read Next Story
							</a>
						</amp-story-cta-layer>
					</amp-story-page>
				)}

				<amp-story-auto-ads>
					<script
						type="application/json"
						dangerouslySetInnerHTML={{
							__html: JSON.stringify({
								"ad-attributes": {
									type: "doubleclick",
									"data-slot": "/23290324739/Manifest-AMP-Stories",
								},
							}),
						}}
					/>
				</amp-story-auto-ads>

				<amp-analytics type="gtag" data-credentials="include">
					<script
						type="application/json"
						dangerouslySetInnerHTML={{
							__html: JSON.stringify({
								vars: {
									gtag_id: "G-3G2PKDJZCD",
									config: {
										"G-3G2PKDJZCD": {
											page_title: data?.title || "",
											page_location: Const.ClientLink + pathname,
										},
									},
								},
								triggers: {
									storyPageView: {
										on: "story-page-visible",
										request: "event",
										vars: {
											event_name: "story_page_view",
											story_page_id: "${storyPageId}",
											story_page_index: "${storyPageIndex}",
										},
									},
									storyComplete: {
										on: "story-last-page-visible",
										request: "event",
										vars: {
											event_name: "story_complete",
										},
									},
								},
							}),
						}}
					/>
				</amp-analytics>
			</amp-story>
		</>
	);
};

export default WebStoryDetail;
WebStoryDetail.config = { amp: true };

export async function getServerSideProps(context) {
	const { category, slug } = context.params;
	const url = `/${slug}`;
	try {
		const storiesRes = await getWebStories(url);

		if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
			return {
				notFound: true,
			};
		}
		const storyData = storiesRes.data.current.data;
		const newObject = {
			title: storyData.title,
			description: "",
			image: storyData.coverImg,
			altName: storyData.altName,
			sequence: -1,
			contributor: [],
			timestamp: storyData.timestamp,
		};

		if (Array.isArray(storyData.slides)) {
			storyData.slides.unshift(newObject);
		}

		return {
			props: {
				data: storyData ?? {},
				previousData: storiesRes.data.previous ?? {},
				nextData: storiesRes.data.next
					? {
							...storiesRes.data.next,
							slug: `/web-stories/${storiesRes.data.next.slug}`,
					  }
					: {},
				breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
				tag: storiesRes.data.current.tag ?? [],
				meta: storiesRes.data.current.meta ?? {},
				pathname: context.resolvedUrl || context.req.url || "",
			},
		};
	} catch (error) {
		console.error("Error fetching data:", error.message);
		console.log("error", error);
		return {
			notFound: true,
		};
	}
}
