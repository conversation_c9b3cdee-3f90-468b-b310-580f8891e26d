import React, { useState } from "react";
import styles from "@/components/webstories/WebStoriesCard.module.css";
import { getWebStoriesCategory } from "@/pages/api/WebstoriesApi";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Layout from "@/components/layout/Layout";

const WebStoriesCategories = ({ stories, title, meta, breadcrumbs }) => {
	return (
		<Layout>
			<SeoHeader title={title} meta={meta} />
			<BreadcrumbSchema breadcrumbs={breadcrumbs} />
			<div
				className={`mx-auto ${styles.webstorySection}`}
				style={{ backgroundColor: "var(--background-color)" }}
			>
				<div className={`${styles.webStoriesContainer} container`}>
					<h1 className={styles.categoryTitle}>Web Stories</h1>

					{stories && stories?.data?.length > 0 ? (
						<div className={styles.storiesGrid}>
							{stories.data.map((story) => {
								// Extract category and subcategory from the slug
								const slugParts = story.slug.split("/");
								const categorySlug = slugParts[1] || "";
								const subcategorySlug = slugParts[2] || "";
								const storySlug = slugParts[3] || "";

								return (
									<a
										target="_blank"
										href={`/web-stories/${categorySlug}/${subcategorySlug}/${storySlug}`}
										key={story._id}
										className={styles.storyCard}
									>
										<div className={styles.storyImageContainer}>
											<img
												src={story.coverImg || "/assets/images/placeholder.jpg"}
												alt={story.title}
												className={styles.storyImage}
											/>
										</div>
										<div className={styles.storyContent}>
											<h5 className={styles.storyTitle}>{story.title}</h5>
											{story?.author?.length > 0 ? (
												<p className={styles.storyAuthor}>
													By {story.author.map((author) => author.name).join(", ")}
												</p>
											) : null}
											{story.timestamp && (
												<p className={styles.storyDate}>
													{new Date(story.timestamp).toLocaleDateString("en-US", {
														year: "numeric",
														month: "short",
														day: "numeric",
													})}
												</p>
											)}
										</div>
									</a>
								);
							})}
						</div>
					) : (
						<div className={styles.emptyState}>
							<h3>No Web Stories Found</h3>
							<p>Check back later for new web stories.</p>
						</div>
					)}
				</div>
			</div>
		</Layout>
	);
};

export async function getServerSideProps() {
	try {
		// Fetch web stories
		const storiesData = await getWebStoriesCategory({
			slug: "",
			limit: 20,
			offset: 0,
		});

		console.log("Stories Data:", storiesData); // Log the stories data

		return {
			props: {
				stories: storiesData?.data || { data: [] },
				title: storiesData?.title || "Web Stories",
				meta: storiesData?.meta || {},
				breadcrumbs: storiesData?.breadcrumbs || [],
			},
		};
	} catch (error) {
		console.error("Error fetching web stories:", error);
		return {
			props: {
				stories: { data: [] },
				title: "Web Stories",
				meta: {},
				breadcrumbs: [],
			},
		};
	}
}

export default WebStoriesCategories;
