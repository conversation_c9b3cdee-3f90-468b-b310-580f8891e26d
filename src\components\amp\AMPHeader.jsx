import React from "react";
import { menus } from "@/helpers/MenuData";
import { FaFacebookF, Fa<PERSON><PERSON><PERSON>, FaYoutube } from "react-icons/fa";
import { PiInstagramLogoFill } from "react-icons/pi";
import { IoSearchOutline } from "react-icons/io5";
import Head from "next/head";
import { Const } from "@/utils/Constants";

// AMP Mobile-Only Header Component
const AMPMobileHeader = () => {
  return (
    <>
      {/* AMP Scripts */}
      <Head>
        <script
          async
          custom-element="amp-sidebar"
          src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"
        ></script>
        <script
          async
          custom-element="amp-bind"
          src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"
        ></script>
      </Head>
      {/* AMP Sidebar State */}
      <amp-state id="sidebarState">
        <script
          type="application/json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({ open: false }),
          }}
        />
      </amp-state>

      <amp-state id="navState">
        <script
          type="application/json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({ mobmenu: null }),
          }}
        />
      </amp-state>
      <div className="tn-nav-rel" next-page-hide>
        <amp-sidebar
          id="mobile-nav"
          layout="nodisplay"
          side="left"
          class="mob-menu"
          on="sidebarClose:AMP.setState({ sidebarState: { open: false } })"
        >
          <div
            style={{
              overflowX: "auto",
              paddingBottom: "80px",
            }}
          >
            {menus.map((item, i) => (
              <>
                {item.submenus.length === 0 ? (
                  <a
                    href={item.link}
                    className={`mob-item mob-open-0}`}
                    key={`mobile-menu-item-${i}`}
                    style={{ textDecoration: "none", color: "#212529" }}
                  >
                    {item.name}
                  </a>
                ) : (
                  <>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: `<div
                        class="mob-item"
                        role="button"
                        tabindex="0"
                        [class]="navState.mobmenu == ${i} ? 'mob-item mob-open-0' : 'mob-item'"
                        on="tap:AMP.setState({ navState: { mobmenu: ${i} } })">
                        ${item.name}
                        <span class="chevron">&rsaquo;</span>
                        </div>
                        `,
                      }}
                    />
                  </>
                )}
              </>
            ))}
            {/* Follow us */}
            <div className="mbf">
              <div className="mob-follows">Follow us</div>
              <div className="mf-icons">
                <a
                  style={{ color: "black" }}
                  href={"https://facebook.com/hollywoodreporterindia"}
                  target="_blank"
                >
                  <FaFacebookF />
                </a>
                <a
                  style={{ color: "black" }}
                  href={"https://instagram.com/hollywoodreporterindia"}
                  target="_blank"
                >
                  <PiInstagramLogoFill />
                </a>
                <a
                  style={{ color: "black" }}
                  href={"https://twitter.com/thrindia_"}
                  target="_blank"
                >
                  <FaTwitter />
                </a>
                <a
                  style={{ color: "black" }}
                  href={"https://www.youtube.com/@HollywoodReporterIndia"}
                  target="_blank"
                >
                  <FaYoutube />
                </a>
              </div>
            </div>
            {/* Bottom  */}
            <div
              className="mbf-artboard-container"
              dangerouslySetInnerHTML={{
                __html: `<div class="mbf-artboard-container" [class]="navState.mobmenu != null ? 'hidden':''">
              <a
                target="_blank"
                class="bo-right-wrapped"
                href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
              >
                <amp-img
                  src="/Artboard-14.jpg"
                  width="192"
                  height="250"
                  alt="The Hollywood Reporter India cover Featuring Superstar Rajinikanth"
                />
              </a>
              <a
                href="https://shop.yudirect.biz/THRIndia/Subscribe.php"
                target="_blank"
              >
                <h6 class="bo-right-click-text">Subscribe</h6>
              </a>
            </div>`,
              }}
            />
          </div>
          {/* Submenu */}
          <div
            dangerouslySetInnerHTML={{
              __html: `
      <div class="mob-submenu submenu-active" [hidden]="navState.mobmenu == null">
        <div class="mob-mbody">
          <div
            class="mbody-head tb-head-trans"
            role="button"
            tabindex="1"
            on="tap:AMP.setState({ navState: { mobmenu: null } })"
          >
            <span class="chevron">&lsaquo;</span>
            <span
              class="title"
              [text]="'${menus
                .map((m) => m.name)
                .join("|")}'.split('|')[navState.mobmenu]"
            ></span>
          </div>
          <div class="mob-body-dynamic">
            ${menus
              .map((menu, index) => {
                if (!menu.submenus.length) return "";
                return `
                  <div [hidden]="navState.mobmenu != ${index}">
                    ${menu.submenus
                      .map(
                        (sub) => `
                          <a class="mob-item" href="${sub.link}" style="text-decoration:none; color:#212529;">
                            ${sub.name}
                          </a>`
                      )
                      .join("")}
                  </div>
                `;
              })
              .join("")}
          </div>
        </div>
        <div class="mbf">
          <div class="mob-follows">Follow us</div>
          <div class="mf-icons">
            <a style="color: black;" href="https://facebook.com/hollywoodreporterindia" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><!-- Icon from Typicons by Stephen Hutchings - https://creativecommons.org/licenses/by-sa/4.0/ --><path fill="currentColor" d="M13 10h3v3h-3v7h-3v-7H7v-3h3V8.745c0-1.189.374-2.691 1.118-3.512Q12.234 3.999 13.904 4H16v3h-2.1c-.498 0-.9.402-.9.899z"/></svg></a>
            <a style="color: black;" href="https://instagram.com/hollywoodreporterindia" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 16 16"><!-- Icon from FormKit Icons by FormKit, Inc - https://github.com/formkit/formkit/blob/master/packages/icons/LICENSE --><path fill="currentColor" d="M8 5.67C6.71 5.67 5.67 6.72 5.67 8S6.72 10.33 8 10.33S10.33 9.28 10.33 8S9.28 5.67 8 5.67M15 8c0-.97 0-1.92-.05-2.89c-.05-1.12-.31-2.12-1.13-2.93c-.82-.82-1.81-1.08-2.93-1.13C9.92 1 8.97 1 8 1s-1.92 0-2.89.05c-1.12.05-2.12.31-2.93 1.13C1.36 3 1.1 3.99 1.05 5.11C1 6.08 1 7.03 1 8s0 1.92.05 2.89c.05 1.12.31 2.12 1.13 2.93c.82.82 1.81 1.08 2.93 1.13C6.08 15 7.03 15 8 15s1.92 0 2.89-.05c1.12-.05 2.12-.31 2.93-1.13c.82-.82 1.08-1.81 1.13-2.93c.06-.96.05-1.92.05-2.89m-7 3.59c-1.99 0-3.59-1.6-3.59-3.59S6.01 4.41 8 4.41s3.59 1.6 3.59 3.59s-1.6 3.59-3.59 3.59m3.74-6.49c-.46 0-.84-.37-.84-.84s.37-.84.84-.84s.84.37.84.84a.8.8 0 0 1-.24.59a.8.8 0 0 1-.59.24Z"/></svg></a>
            <a style="color: black;" href="https://twitter.com/thrindia_" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><!-- Icon from Typicons by Stephen Hutchings - https://creativecommons.org/licenses/by-sa/4.0/ --><path fill="currentColor" d="M18.89 7.012q1.212-.743 1.605-2.034a8.7 8.7 0 0 1-2.351.861c-.703-.756-1.593-1.14-2.66-1.14c-1.043 0-1.924.366-2.643 1.078a3.56 3.56 0 0 0-1.076 2.605c0 .309.039.585.117.819C8.806 9.096 6.26 7.82 4.254 5.364c-.34.601-.51 1.213-.51 1.846q0 1.953 1.645 3.089c-.625-.053-1.176-.211-1.645-.47c0 .929.273 1.705.82 2.388a3.62 3.62 0 0 0 2.115 1.291q-.47.119-.979.118q-.468.001-.664-.083c.23.757.664 1.371 1.291 1.841a3.65 3.65 0 0 0 2.152.743c-1.332 1.045-2.855 1.562-4.578 1.562c-.422 0-.721-.006-.902-.038C4.696 18.753 6.585 19.3 8.675 19.3q3.207 0 5.674-1.626c1.645-1.078 2.859-2.408 3.639-3.974a10.8 10.8 0 0 0 1.172-4.892V8.34A7.8 7.8 0 0 0 21 6.419a8 8 0 0 1-2.11.593"/></svg></i></a>
            <a style="color: black;" href="https://www.youtube.com/@HollywoodReporterIndia" target="_blank"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><!-- Icon from Typicons by Stephen Hutchings - https://creativecommons.org/licenses/by-sa/4.0/ --><path fill="currentColor" d="M22.8 8.6c-.2-1.5-.4-2.6-1-3C21.2 5.1 16 5 12 5s-9.2.1-9.8.6c-.6.4-.8 1.5-1 3S1 11 1 12s0 1.9.2 3.4s.4 2.6 1 3c.6.5 5.8.6 9.8.6s9.2-.1 9.8-.6c.6-.4.8-1.5 1-3S23 13 23 12s0-1.9-.2-3.4m-12.8 7V8.4l6 3.6z"/></svg></a>
          </div>
        </div>
        <div class="mbf-artboard-container">
          <a target="_blank" class="bo-right-wrapped" href="https://shop.yudirect.biz/THRIndia/Subscribe.php">
            <amp-img
              src="/Artboard-14.jpg"
              width="192"
              height="250"
              alt="The Hollywood Reporter India cover Featuring Superstar Rajinikanth"
            ></amp-img>
          </a>
          <a href="https://shop.yudirect.biz/THRIndia/Subscribe.php" target="_blank">
            <h6 class="bo-right-click-text">Subscribe</h6>
          </a>
        </div>
      </div>
    `,
            }}
          />
        </amp-sidebar>
        {/* Navbar */}
        <div className="nav-wrapper">
          <a
            style={{ visibility: "hidden" }}
            href={`${Const.ClientLink}/result`}
            className="search_icon"
          >
            <IoSearchOutline />
          </a>
          <a href={"/"} className="logo">
            <amp-img
              width="160"
              height="70"
              src="/Thr_logo_updated.png"
              alt="The Hollywood Reporter India Logo"
            />
          </a>
          <div className="tn-ham">
            <div className="three col">
              <div
                dangerouslySetInnerHTML={{
                  __html: `
      <div
        class="hamburger"
        [class]="sidebarState.open ? 'hamburger is-active' : 'hamburger'"
        id="hamburger-1"
        role="button"
        tabindex="2"
        on="tap:mobile-nav.toggle,AMP.setState({ sidebarState: { open: !sidebarState.open } })"
      >
        <span class="line"></span>
        <span class="line"></span>
        <span class="line"></span>
      </div>
    `,
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AMPMobileHeader;
