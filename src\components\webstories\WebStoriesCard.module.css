.webStoriesContainer {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.categoryTitle {
	font-size: 3rem;
	font-family: var(--font-family-primary);
	font-weight: 500;
	text-align: center;
	margin-bottom: 3rem;
	color: var(--color);
	text-transform: uppercase;
	letter-spacing: 1px;
}

.storiesGrid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	gap: 2rem;
	margin-bottom: 3rem;
}

.storyCard {
	text-decoration: none;
	color: inherit;
	display: block;
}

.storyCard:hover {
	text-decoration: none;
	color: inherit;
}

.storyCard:hover .storyTitle {
	color: var(--primary-color);
}

.storyImageContainer {
	width: 100%;
	position: relative;
	margin-bottom: 10px;
	overflow: hidden;
	transition: all 0.3s ease-in-out;
	aspect-ratio: 1;
	height: 30rem;
}

.storyImage {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: top;
}

.storyContent {
	padding: 0;
}

.storyTitle {
	font-family: "kepler-std-semicondensed-dis", serif;
	font-weight: 600;
	margin-bottom: 0.5rem;
	line-height: 1.3;
	color: var(--color);
	transition: color 0.3s ease;
	font-size: 26px;
}

.storyAuthor {
	display: block;
	font-size: 12px;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary) !important;
	font-weight: 700;
	letter-spacing: 1px;
	font-style: normal;
	text-transform: uppercase;
	margin-bottom: 0px;
}

.storyDate {
	display: block;
	font-size: 12px;
	color: var(--secondary-color);
	font-family: var(--font-family-secondary) !important;
	font-weight: 700;
	letter-spacing: 1px;
	font-style: normal;
}

.webstorySection {
	padding: 2.5rem 0 1.5rem;
}

.subcategoryNav {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	gap: 0.5rem;
	margin-bottom: 2rem;
}

.subcategoryButton {
	padding: 0.5rem 1rem;
	border-radius: 2rem;
	font-size: 0.875rem;
	cursor: pointer;
	transition: all 0.2s ease;
	border: none;
}

.subcategoryButtonActive {
	background-color: var(--active-btn-bg, #000);
	color: var(--active-btn-text, white);
}

.subcategoryButtonInactive {
	background-color: var(--inactive-btn-bg, #f1f1f1);
	color: var(--text-color, #333);
}

.loadMoreButton {
	display: block;
	margin: 2rem auto 0;
	padding: 0.75rem 2rem;
	background-color: var(--btn-bg, #000);
	color: var(--btn-text, white);
	border: none;
	border-radius: 2rem;
	font-size: 1rem;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.loadMoreButton:hover {
	background-color: var(--btn-hover-bg, #333);
}

.loadMoreButton:disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.loaderContainer {
	display: flex;
	justify-content: center;
	padding: 2rem 0;
}

.loader {
	border: 4px solid var(--loader-border, rgba(0, 0, 0, 0.1));
	border-left-color: var(--text-color, #000);
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

@media screen and (max-width: 900px) {
	.webstorySection {
		padding: 2.5rem 0 1.5rem;
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.webStoriesContainer {
		padding: 0 15px;
	}

	.categoryTitle {
		font-size: 2rem;
		margin-bottom: 2rem;
	}

	.storiesGrid {
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
		gap: 1.5rem;
	}

	.storyOverlay {
		padding: 1rem;
	}

	.storyTitle {
		font-size: 23px;
	}

	.storyAuthor {
		font-size: 0.8rem;
	}
}

@media (max-width: 480px) {
	.storiesGrid {
		grid-template-columns: 1fr;
		gap: 1rem;
	}

	.categoryTitle {
		font-size: 1.75rem;
	}
}

/* Loading state */
.loadingCard {
	background: var(--card-bg-color, #fff);
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loadingSkeleton {
	aspect-ratio: 9 / 16;
	background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
	background-size: 200% 100%;
	animation: loading 1.5s infinite;
}

@keyframes loading {
	0% {
		background-position: 200% 0;
	}
	100% {
		background-position: -200% 0;
	}
}

/* Empty state */
.emptyState {
	text-align: center;
	padding: 4rem 2rem;
	color: var(--text-color);
}

.emptyState h3 {
	font-size: 1.5rem;
	font-family: "neue-haas-grotesk-display", sans-serif;
	font-weight: 600;
	margin-bottom: 1rem;
}

.emptyState p {
	font-size: 1rem;
	font-family: "neue-haas-grotesk-display", sans-serif;
	opacity: 0.7;
}
